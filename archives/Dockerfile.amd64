FROM --platform=linux/amd64 python:3.9-slim AS builder

WORKDIR /app

# Install build dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    libffi-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements file
COPY requirements.txt .

# Install dependencies into a virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Second stage: runtime image
FROM --platform=linux/amd64 python:3.9-slim

WORKDIR /app

# Copy virtual environment from builder stage
COPY --from=builder /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy application code
COPY app.py .
COPY detect_hardware.py .
COPY static/ ./static/
COPY index.html .

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Set Vintern-specific environment variables
ENV VINTERN_STREAM=false
ENV VINTERN_MAX_TOKENS=1024
ENV VINTERN_TEMPERATURE=0.0
ENV VINTERN_DO_SAMPLE=false
ENV VINTERN_NUM_BEAMS=3
ENV VINTERN_REPETITION_PENALTY=2.5

# Expose port for API
EXPOSE 8000

# Command to run the application
CMD ["python", "app.py"]
