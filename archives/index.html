<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <base href="/">
    <title>Demo OCR các văn bản cho Co-opBank</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <!-- GitHub Markdown CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/github-markdown-css@5.2.0/github-markdown.min.css">
    <style>
        body {
            padding-top: 2rem;
            background: linear-gradient(135deg, #e0e7ff 0%, #f8fafc 100%);
            min-height: 100vh;
        }

        .card {
            box-shadow: 0 4px 16px rgba(0, 60, 120, 0.08);
            border-radius: 1rem;
            border: none;
            margin-bottom: 2rem;
        }

        .card-header {
            background: linear-gradient(90deg, #2563eb 0%, #38bdf8 100%);
            color: #fff;
            border-radius: 1rem 1rem 0 0;
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        .form-select, .form-control {
            border-radius: 0.5rem;
        }

        .drag-area {
            height: 220px;
            border: 2px dashed #2563eb;
            border-radius: 0.75rem;
            background: #f1f5f9;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            cursor: pointer;
            transition: background 0.3s, border-color 0.3s;
        }

        .drag-area:hover, .drag-active {
            background: #dbeafe;
            border-color: #38bdf8;
        }

        .preview-container {
            max-height: 300px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f0f9ff;
            border-radius: 0.75rem;
            margin-bottom: 1rem;
            border: 1px solid #e0e7ef;
        }

        #imagePreview {
            max-width: 100%;
            max-height: 300px;
            object-fit: contain;
        }

        .drag-area {
            height: 300px;
            border: 2px dashed #0d6efd;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            background-color: #f8f9fa;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .drag-area:hover {
            background-color: #e9ecef;
        }

        .drag-active {
            background-color: #d1e7ff;
            border-color: #0a58ca;
        }

        .file-info {
            font-size: 0.95rem;
            color: #64748b;
        }

        #markdownOutput {
            border: 1.5px solid #2563eb;
            background: #f8fafc;
            border-radius: 0.75rem;
            padding: 1.5rem;
            min-height: 220px;
            max-height: 500px;
            overflow-y: auto;
            font-size: 1.1rem;
            color: #22223b;
            box-shadow: 0 2px 8px rgba(37,99,235,0.06);
        }

        /* Markdown styling */
        .markdown-body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
            font-size: 1.1rem;
            line-height: 1.5;
            word-wrap: break-word;
        }

        .markdown-body img {
            max-width: 100%;
            box-sizing: border-box;
        }

        .markdown-body h1,
        .markdown-body h2,
        .markdown-body h3 {
            color: #2563eb;
            font-weight: 700;
            border-bottom: 1px solid #eaecef;
            padding-bottom: 0.3em;
        }

        .markdown-body table {
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 16px;
            border: 1.5px solid #2563eb;
            background: #f1f5f9;
        }

        .markdown-body table th,
        .markdown-body table td {
            padding: 6px 13px;
            border: 1px solid #dfe2e5;
        }

        .markdown-body table tr {
            background-color: #fff;
            border-top: 1px solid #c6cbd1;
        }

        .markdown-body table tr:nth-child(2n) {
            background-color: #f6f8fa;
        }

        .markdown-body pre {
            padding: 16px;
            overflow: auto;
            font-size: 85%;
            line-height: 1.45;
            background: #e0e7ff !important;
            color: #1e293b;
            border-radius: 0.4rem;
        }

        .markdown-body code {
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            background-color: rgba(27, 31, 35, 0.05);
            border-radius: 3px;
            background: #e0e7ff !important;
            color: #1e293b;
            border-radius: 0.4rem;
        }

        .loading {
            display: none;
            margin-bottom: 1rem;
            color: #2563eb;
        }

        .spinner-border {
            width: 1.5rem;
            height: 1.5rem;
            margin-right: 0.5rem;
        }

        .prompt-input {
            margin-bottom: 1rem;
        }

        .output-actions {
            display: flex;
            gap: 0.5rem;
        }

        .template-selection label {
            font-weight: 500;
            color: #2563eb;
        }

        .prompt-input label {
            font-weight: 500;
            color: #2563eb;
        }

        .btn-primary {
            background: linear-gradient(90deg, #2563eb 0%, #38bdf8 100%);
            border: none;
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        .btn-primary:active, .btn-primary:focus {
            background: #2563eb;
        }

        .alert-info {
            background: #e0f2fe;
            color: #0369a1;
            border: 1px solid #38bdf8;
        }

        .alert-danger {
            background: #fee2e2;
            color: #b91c1c;
            border: 1px solid #f87171;
        }

        .output-actions .btn {
            border-radius: 0.5rem;
        }

        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2563eb;
        }

        .form-label {
            color: #0e7490;
        }

        .file-info {
            font-size: 0.95rem;
            color: #64748b;
        }

        .markdown-body {
            font-size: 1.1rem;
        }

        @media (max-width: 767px) {
            .card-body, #markdownOutput {
                padding: 1rem;
            }
        }

        /* Custom styles for requested UI changes */
        .card-title.mb-0 {
            color: #fff !important; /* Make 'Tải Lên Hình Ảnh' white */
        }
        #outputTitle.card-title.mb-0 {
            color: #fff !important; /* Make 'Kết Quả Văn Bản Hành Chính' white */
        }
        .output-actions .btn.btn-outline-primary,
        .output-actions .btn.btn-outline-secondary {
            color: #000 !important; /* Button text black */
            border-color: #000 !important; /* Button border black */
            background-color: #fff !important; /* Button background white */
        }
        .output-actions .btn.btn-outline-primary.active,
        .output-actions .btn.btn-outline-primary:active,
        .output-actions .btn.btn-outline-primary:focus {
            color: #fff !important;
            background-color: #000 !important;
            border-color: #000 !important;
        }
        .output-actions .btn.btn-outline-secondary:active,
        .output-actions .btn.btn-outline-secondary:focus {
            color: #fff !important;
            background-color: #000 !important;
            border-color: #000 !important;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="row">
            <div class="col-md-12 text-center mb-4">
                <h1 class="display-5">Demo OCR các văn bản cho Co-opBank</h1>
                <p class="lead">Chuyển đổi hình ảnh thành văn bản markdown sử dụng mô hình Vintern-1B-v3.5</p>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0"><i class="bi bi-image"></i> Tải Lên Hình Ảnh</h5>
                    </div>
                    <div class="card-body">
                        <div id="dragArea" class="drag-area">
                            <i class="bi bi-cloud-arrow-up" style="font-size: 3rem;"></i>
                            <p>Kéo thả hình ảnh/PDF hoặc <strong>Nhấp để tải lên</strong></p>
                            <p class="file-info">Định dạng hỗ trợ: JPG, PNG, GIF, BMP, PDF</p>
                            <input type="file" id="fileInput" accept="image/*,.pdf" hidden>
                        </div>

                        <div class="preview-container mt-3" style="display: none;">
                            <img id="imagePreview" src="#" alt="Xem trước hình ảnh">
                        </div>

                        <div class="template-selection mt-3">
                            <label for="templateSelect" class="form-label">Template (Tùy chọn):</label>
                            <select id="templateSelect" class="form-select">
                                <option value="">Không sử dụng template</option>
                                <option value="json">JSON - Bìa Hồ Sơ</option>
                                <option value="hoso_cover">Hồ sơ lưu trữ - Bìa hồ sơ</option>
                                <option value="vanban_hanhchinh">Văn bản hành chính</option>
                            </select>
                        </div>

                        <!-- Template Helper Guide -->
                        <div id="templateHelper" class="mt-3" style="display: none;">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0"><i class="bi bi-info-circle"></i> Hướng dẫn Template</h6>
                                </div>
                                <div class="card-body">
                                    <div id="templateHelperContent">
                                        <!-- Content will be dynamically updated -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="prompt-input mt-3">
                            <label for="promptInput" class="form-label">Lời nhắc (Prompt):</label>
                            <div id="promptEditor"
                                style="height: 200px; border: 1px solid #ced4da; border-radius: 0.375rem;"></div>
                            <small class="form-text text-muted">
                                <i class="bi bi-info-circle"></i> Bạn có thể chỉnh sửa prompt để tùy chỉnh cấu trúc kết
                                quả đầu ra.
                                Hỗ trợ syntax highlighting và auto-completion.
                            </small>
                        </div>

                        <div id="estimatedTime" class="alert alert-info mt-3" style="display: none;">
                            <i class="bi bi-clock"></i> <span id="estimatedTimeText">Ước tính thời gian xử lý: --</span>
                        </div>

                        <button id="processBtn" class="btn btn-primary w-100 mt-3" disabled>
                            <i class="bi bi-magic"></i> Xử Lý Hình Ảnh/PDF
                        </button>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 id="outputTitle" class="card-title mb-0"><i class="bi bi-markdown"></i> Kết Quả Markdown
                        </h5>
                        <div class="output-actions">
                            <div class="btn-group" role="group" aria-label="Chế độ xem">
                                <button id="viewRenderedBtn" type="button"
                                    class="btn btn-sm btn-outline-primary active">
                                    <i class="bi bi-eye"></i> Hiển Thị
                                </button>
                                <button id="viewSourceBtn" type="button" class="btn btn-sm btn-outline-primary">
                                    <i class="bi bi-code-slash"></i> Mã Nguồn
                                </button>
                            </div>
                            <button id="copyBtn" class="btn btn-sm btn-outline-secondary" disabled>
                                <i class="bi bi-clipboard"></i> Sao Chép
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="loading" id="loadingIndicator">
                            <div class="d-flex align-items-center justify-content-center">
                                <div class="spinner-border text-primary me-3" role="status"></div>
                                <div>
                                    <div>Đang xử lý hình ảnh, vui lòng đợi...</div>
                                    <div id="progressInfo" class="small text-muted mt-1" style="display: none;">
                                        <div class="progress mt-2" style="height: 4px;">
                                            <div id="progressBar" class="progress-bar" role="progressbar"
                                                style="width: 0%"></div>
                                        </div>
                                        <div id="progressText" class="mt-1">Thời gian còn lại: --</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="markdownOutput" class="markdown-body">
                            <div class="text-center text-muted">
                                <i class="bi bi-file-earmark-text" style="font-size: 3rem;"></i>
                                <p>Tải lên hình ảnh để xem văn bản được trích xuất tại đây</p>
                            </div>
                        </div>
                        <div id="markdownSource" class="d-none">
                            <pre class="p-3 bg-light border rounded"><code id="sourceCode"></code></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0"><i class="bi bi-gear"></i> Cài Đặt API</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="apiUrlInput" class="form-label">Địa chỉ API:</label>
                            <input type="text" id="apiUrlInput" class="form-control" value="https://ocr.csharpp.com">
                        </div>
                        <div class="row">
                            <div class="col">
                                <div class="mb-3">
                                    <label for="maxTokensInput" class="form-label">Số Token Tối Đa:</label>
                                    <input type="number" id="maxTokensInput" class="form-control" value="1024" min="1"
                                        max="4096">
                                </div>
                            </div>
                            <div class="col">
                                <div class="mb-3">
                                    <label for="temperatureInput" class="form-label">Nhiệt Độ (Temperature):</label>
                                    <input type="number" id="temperatureInput" class="form-control" value="0.0" min="0"
                                        max="1" step="0.1">
                                </div>
                            </div>
                            <div class="col">
                                <div class="mb-3">
                                    <label for="beamsInput" class="form-label">Số Beam:</label>
                                    <input type="number" id="beamsInput" class="form-control" value="3" min="1"
                                        max="10">
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Markdown-it for rendering markdown -->
    <script src="https://cdn.jsdelivr.net/npm/markdown-it@13.0.1/dist/markdown-it.min.js"></script>
    <!-- Highlight.js for code syntax highlighting -->
    <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.7.0/lib/highlight.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.7.0/styles/github.min.css">
    <!-- Monaco Editor -->
    <script src="https://cdn.jsdelivr.net/npm/monaco-editor@0.44.0/min/vs/loader.js"></script>
    <script>
        // Initialize markdown-it without highlight.js for now
        const md = window.markdownit({
            html: true,
            linkify: true,
            typographer: true
        });

        // Make md available globally
        window.md = md;

        // Wait for all scripts to load before initializing
        document.addEventListener('DOMContentLoaded', function () {

            // Elements
            const dragArea = document.getElementById('dragArea');
            const fileInput = document.getElementById('fileInput');
            const imagePreview = document.getElementById('imagePreview');
            const previewContainer = document.querySelector('.preview-container');
            const processBtn = document.getElementById('processBtn');
            const markdownOutput = document.getElementById('markdownOutput');
            const markdownSource = document.getElementById('markdownSource');
            const sourceCode = document.getElementById('sourceCode');
            const viewRenderedBtn = document.getElementById('viewRenderedBtn');
            const viewSourceBtn = document.getElementById('viewSourceBtn');
            const copyBtn = document.getElementById('copyBtn');
            const loadingIndicator = document.getElementById('loadingIndicator');
            const apiUrlInput = document.getElementById('apiUrlInput');
            const maxTokensInput = document.getElementById('maxTokensInput');
            const temperatureInput = document.getElementById('temperatureInput');
            const beamsInput = document.getElementById('beamsInput');
            const templateSelect = document.getElementById('templateSelect');
            const templateHelper = document.getElementById('templateHelper');
            const templateHelperContent = document.getElementById('templateHelperContent');
            const estimatedTimeDiv = document.getElementById('estimatedTime');
            const estimatedTimeText = document.getElementById('estimatedTimeText');
            const progressInfo = document.getElementById('progressInfo');
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            const outputTitle = document.getElementById('outputTitle');

            // Store the original markdown text
            let originalMarkdownText = '';

            // Monaco Editor instance
            let promptEditor = null;



            // Template information and helpers
            const templateInfo = {
                '': {
                    title: 'Kết Quả Markdown',
                    icon: 'bi-markdown',
                    prompt: 'Trích xuất thông tin chính trong ảnh và trả về dạng markdown.',
                    helper: null
                },
                'json': {
                    title: 'Kết Quả JSON',
                    icon: 'bi-braces',
                    prompt: 'Trích xuất thông tin từ hình ảnh và trả về dưới dạng JSON với cấu trúc:\n{\n  "title": "tiêu đề chính",\n  "content": "nội dung chính",\n  "details": {\n    "bắt đầu": "Thời gian bắt đầu",\n    "kết thúc": "Thời gian kết thúc",\n    "thời hạn bảo quản": "Thời hạn bảo quản"\n  }\n}',
                    helper: {
                        title: 'Hướng dẫn tùy chỉnh JSON',
                        description: 'Bạn có thể tùy chỉnh cấu trúc JSON theo nhu cầu',
                        examples: [
                            'Thay đổi tên các trường trong JSON (ví dụ: "name", "address", "phone")',
                            'Tùy chỉnh cấu trúc nested objects theo dữ liệu cần trích xuất'
                        ],
                        tips: [
                            'Sửa đổi cấu trúc JSON trong prompt để phù hợp với dữ liệu của bạn',
                            'Có thể thêm mô tả cụ thể cho từng trường cần trích xuất',
                            'Ví dụ: thay "details" thành "contact_info" cho danh thiếp'
                        ]
                    }
                },
                'hoso_cover': {
                    title: 'Kết Quả Hồ Sơ Lưu Trữ',
                    icon: 'bi-archive',
                    prompt: 'Trích xuất các trường thông tin trên trang bìa hồ sơ lưu trữ và trả về JSON:\n{\n  "tieu_de_ho_so": "Tiêu đề hoặc chủ đề chính",,\n  "tg_bat_dau": "Thời gian bắt đầu (dd/mm/yyyy hoặc yyyy)",\n  "tg_ket_thuc": "Thời gian kết thúc (dd/mm/yyyy hoặc yyyy)",\n  "thoi_han_bao_quan": "Thời hạn bảo quản"\n}',
                    helper: {
                        title: 'Hướng dẫn trích xuất bìa hồ sơ',
                        description: 'Trích xuất các trường như tiêu đề hồ sơ, thời gian bắt đầu, kết thúc, thời hạn bảo quản từ trang bìa hồ sơ lưu trữ.',
                        examples: [
                            'Trích xuất đúng các trường như trên mẫu bìa hồ sơ',
                            'Nếu không có trường nào, để giá trị là null hoặc chuỗi rỗng',
                            'Có thể bổ sung trường nếu mẫu hồ sơ có thêm thông tin khác'
                        ],
                        tips: [
                            'Kiểm tra kỹ định dạng ngày tháng',
                            'Có thể dùng cho nhiều loại bìa hồ sơ khác nhau',
                            'Nếu trường không rõ, để giá trị null hoặc rỗng'
                        ]
                    }
                },
                'vanban_hanhchinh': {
                    title: 'Kết Quả Văn Bản Hành Chính',
                    icon: 'bi-file-earmark-text',
                    prompt: 'Trích xuất các trường thông tin chính của văn bản hành chính và trả về JSON:\n{\n  "co_quan_ban_hanh": "Tên cơ quan ban hành",\n  "the_loai_van_ban": "Thể loại văn bản",\n  "so_van_ban": "Số văn bản",\n  "content": "Nội dung chính",\n  "ngay_ky": "Ngày ký (dd/mm/yyyy hoặc yyyy-mm-dd)",\n  "nguoi_ky": "Người ký"\n}',
                    helper: {
                        title: 'Hướng dẫn trích xuất văn bản hành chính',
                        description: 'Trích xuất các trường như cơ quan ban hành, số văn bản, ký hiệu, ngày ký, thể loại văn bản, người ký từ văn bản hành chính.',
                        examples: [
                            'Trích xuất đúng các trường như trên mẫu văn bản hành chính',
                            'Nếu không có trường nào, để giá trị là null hoặc chuỗi rỗng',
                            'Có thể bổ sung trường nếu mẫu văn bản có thêm thông tin khác'
                        ],
                        tips: [
                            'Kiểm tra kỹ định dạng ngày tháng',
                            'Có thể dùng cho nhiều loại văn bản hành chính khác nhau',
                            'Nếu trường không rõ, để giá trị null hoặc rỗng'
                        ]
                    }
                }
            };

            // Function to get prompt value from Monaco Editor
            function getPromptValue() {
                return promptEditor ? promptEditor.getValue() : '';
            }

            // Function to update output title based on selected template
            function updateOutputTitle() {
                const selectedTemplate = templateSelect.value;
                const info = templateInfo[selectedTemplate] || templateInfo[''];
                outputTitle.innerHTML = `<i class="bi ${info.icon}"></i> ${info.title}`;
            }

            // Function to update prompt and helper when template changes
            function updateTemplatePromptAndHelper() {
                const selectedTemplate = templateSelect.value;
                const info = templateInfo[selectedTemplate] || templateInfo[''];

                // Update prompt editor
                if (promptEditor) {
                    promptEditor.setValue(info.prompt);

                    // Set appropriate language based on template
                    let language = 'markdown';
                    if (selectedTemplate === 'json' || selectedTemplate === 'invoice' || selectedTemplate === 'form') {
                        language = 'json';
                    } else if (selectedTemplate === 'yaml') {
                        language = 'yaml';
                    }
                    monaco.editor.setModelLanguage(promptEditor.getModel(), language);
                }

                // Update helper display
                if (info.helper) {
                    templateHelperContent.innerHTML = `
                    <h6 class="text-primary mb-3"><i class="bi bi-gear"></i> ${info.helper.title}</h6>
                    <div class="alert alert-light border-primary">
                        <p class="mb-0"><i class="bi bi-info-circle text-primary"></i> ${info.helper.description}</p>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-success"><i class="bi bi-lightbulb"></i> Cách tùy chỉnh:</h6>
                            <ul class="list-unstyled">
                                ${info.helper.examples.map(example => `<li class="mb-2"><i class="bi bi-arrow-right text-success"></i> ${example}</li>`).join('')}
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-info"><i class="bi bi-exclamation-triangle"></i> Gợi ý:</h6>
                            <ul class="list-unstyled">
                                ${info.helper.tips.map(tip => `<li class="mb-2"><i class="bi bi-check2 text-info"></i> ${tip}</li>`).join('')}
                            </ul>
                        </div>
                    </div>

                    <div class="alert alert-warning mt-3">
                        <small><i class="bi bi-pencil"></i> <strong>Hướng dẫn:</strong> Bạn có thể chỉnh sửa prompt ở trên để tùy chỉnh cấu trúc kết quả theo ý muốn. Template chỉ là gợi ý ban đầu.</small>
                    </div>
                `;
                    templateHelper.style.display = 'block';
                } else {
                    templateHelper.style.display = 'none';
                }

                // Update output title
                updateOutputTitle();
            }

            // Request timing management
            class RequestTimingManager {
                constructor() {
                    this.storageKey = 'ocr_request_timings';
                    this.maxStoredTimings = 50; // Keep last 50 timings
                }

                // Get stored timings from localStorage
                getStoredTimings() {
                    try {
                        const stored = localStorage.getItem(this.storageKey);
                        return stored ? JSON.parse(stored) : [];
                    } catch (e) {
                        console.error('Error reading stored timings:', e);
                        return [];
                    }
                }

                // Save timing to localStorage
                saveRequestTiming(startTime, endTime, fileType = 'image', fileSize = 0) {
                    const duration = endTime - startTime;
                    const timing = {
                        startTime,
                        endTime,
                        duration,
                        fileType,
                        fileSize,
                        timestamp: Date.now()
                    };

                    const timings = this.getStoredTimings();
                    timings.push(timing);

                    // Keep only the most recent timings
                    if (timings.length > this.maxStoredTimings) {
                        timings.splice(0, timings.length - this.maxStoredTimings);
                    }

                    try {
                        localStorage.setItem(this.storageKey, JSON.stringify(timings));
                    } catch (e) {
                        console.error('Error saving timing:', e);
                    }
                }

                // Calculate estimated processing time based on historical data
                getEstimatedTime(fileType = 'image', fileSize = 0) {
                    const timings = this.getStoredTimings();

                    if (timings.length === 0) {
                        return { estimated: 30000, confidence: 'thấp' }; // Default 30 seconds
                    }

                    // Filter timings by file type if available
                    const relevantTimings = timings.filter(t => t.fileType === fileType);
                    const timingsToUse = relevantTimings.length >= 3 ? relevantTimings : timings;

                    // Calculate average duration
                    const totalDuration = timingsToUse.reduce((sum, t) => sum + t.duration, 0);
                    const averageDuration = totalDuration / timingsToUse.length;

                    // Calculate confidence based on number of samples
                    let confidence = 'thấp';
                    if (timingsToUse.length >= 10) confidence = 'cao';
                    else if (timingsToUse.length >= 5) confidence = 'trung bình';

                    // Adjust for file size if provided (rough estimation)
                    let estimatedDuration = averageDuration;
                    if (fileSize > 0 && timingsToUse.some(t => t.fileSize > 0)) {
                        const avgFileSize = timingsToUse
                            .filter(t => t.fileSize > 0)
                            .reduce((sum, t) => sum + t.fileSize, 0) /
                            timingsToUse.filter(t => t.fileSize > 0).length;

                        if (avgFileSize > 0) {
                            const sizeRatio = fileSize / avgFileSize;
                            estimatedDuration = averageDuration * Math.sqrt(sizeRatio); // Square root for diminishing returns
                        }
                    }

                    return {
                        estimated: Math.max(5000, Math.min(300000, estimatedDuration)), // Between 5s and 5min
                        confidence,
                        sampleCount: timingsToUse.length
                    };
                }

                // Format duration for display
                formatDuration(milliseconds) {
                    const seconds = Math.floor(milliseconds / 1000);
                    const minutes = Math.floor(seconds / 60);

                    if (minutes > 0) {
                        return `${minutes}m ${seconds % 60}s`;
                    }
                    return `${seconds}s`;
                }
            }

            const timingManager = new RequestTimingManager();

            // Progress tracking
            let progressInterval = null;

            function startProgressTracking(estimatedDuration) {
                const startTime = Date.now();
                progressInfo.style.display = 'block';

                progressInterval = setInterval(() => {
                    const elapsed = Date.now() - startTime;
                    const progress = Math.min((elapsed / estimatedDuration) * 100, 95); // Cap at 95% until completion
                    const remaining = Math.max(0, estimatedDuration - elapsed);

                    progressBar.style.width = `${progress}%`;
                    progressText.textContent = `Thời gian còn lại: ${timingManager.formatDuration(remaining)}`;

                    // If we've exceeded the estimated time, slow down the progress
                    if (progress >= 95) {
                        clearInterval(progressInterval);
                        progressText.textContent = 'Đang hoàn thành...';
                    }
                }, 1000);
            }

            function stopProgressTracking() {
                if (progressInterval) {
                    clearInterval(progressInterval);
                    progressInterval = null;
                }
                progressBar.style.width = '100%';
                progressText.textContent = 'Hoàn thành!';

                setTimeout(() => {
                    progressInfo.style.display = 'none';
                    progressBar.style.width = '0%';
                }, 2000);
            }

            // Event listeners for drag and drop
            ['dragover', 'dragenter'].forEach(eventName => {
                dragArea.addEventListener(eventName, (e) => {
                    e.preventDefault();
                    dragArea.classList.add('drag-active');
                });
            });

            ['dragleave', 'dragend'].forEach(eventName => {
                dragArea.addEventListener(eventName, () => {
                    dragArea.classList.remove('drag-active');
                });
            });

            dragArea.addEventListener('drop', (e) => {
                e.preventDefault();
                dragArea.classList.remove('drag-active');

                const file = e.dataTransfer.files[0];
                if (file && (file.type.match('image.*') || file.type === 'application/pdf')) {
                    handleFile(file);
                }
            });

            dragArea.addEventListener('click', () => {
                fileInput.click();
            });

            fileInput.addEventListener('change', () => {
                const file = fileInput.files[0];
                if (file) {
                    handleFile(file);
                }
            });

            // Handle file selection
            function handleFile(file) {
                if (file && (file.type.match('image.*') || file.type === 'application/pdf')) {
                    if (file.type === 'application/pdf') {
                        // Handle PDF file
                        imagePreview.src = 'data:image/svg+xml;base64,' + btoa(`
                        <svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                            <polyline points="14,2 14,8 20,8"></polyline>
                            <line x1="16" y1="13" x2="8" y2="13"></line>
                            <line x1="16" y1="17" x2="8" y2="17"></line>
                            <polyline points="10,9 9,9 8,9"></polyline>
                            <text x="12" y="15" text-anchor="middle" font-size="3" fill="currentColor">PDF</text>
                        </svg>
                    `);
                        previewContainer.style.display = 'flex';
                        processBtn.disabled = false;

                        // Store the file for upload
                        window.selectedFile = file;
                    } else {
                        // Handle image file
                        const reader = new FileReader();

                        reader.onload = (e) => {
                            imagePreview.src = e.target.result;
                            previewContainer.style.display = 'flex';
                            processBtn.disabled = false;

                            // Clear stored file for image uploads
                            window.selectedFile = null;
                        };

                        reader.readAsDataURL(file);
                    }

                    // Reset the output areas
                    markdownOutput.innerHTML = '<div class="text-center text-muted"><p>Click "Process Image/PDF" to extract text</p></div>';
                    sourceCode.textContent = '';
                    originalMarkdownText = '';
                    copyBtn.disabled = true;

                    // Show estimated processing time
                    const fileType = file.type === 'application/pdf' ? 'pdf' : 'image';
                    const fileSize = file.size;
                    const estimation = timingManager.getEstimatedTime(fileType, fileSize);

                    estimatedTimeText.textContent = `Ước tính thời gian xử lý: ${timingManager.formatDuration(estimation.estimated)} (độ tin cậy của ước tính: ${estimation.confidence})`;
                    estimatedTimeDiv.style.display = 'block';

                    // Switch to rendered view
                    viewRenderedBtn.click();

                    // Update output title based on selected template
                    updateOutputTitle();
                }
            }

            // Update prompt, helper and output title when template changes
            templateSelect.addEventListener('change', updateTemplatePromptAndHelper);

            // Initialize prompt, helper and output title on page load
            updateTemplatePromptAndHelper();

            // Toggle between rendered and source view
            viewRenderedBtn.addEventListener('click', () => {
                viewRenderedBtn.classList.add('active');
                viewSourceBtn.classList.remove('active');
                markdownOutput.classList.remove('d-none');
                markdownSource.classList.add('d-none');
            });

            viewSourceBtn.addEventListener('click', () => {
                viewSourceBtn.classList.add('active');
                viewRenderedBtn.classList.remove('active');
                markdownSource.classList.remove('d-none');
                markdownOutput.classList.add('d-none');
            });

            // Function to render markdown
            function renderMarkdown(text) {
                // Store original text
                originalMarkdownText = text;

                // Render markdown to HTML
                const renderedHtml = md.render(text);

                // Update both views
                markdownOutput.innerHTML = renderedHtml;
                sourceCode.textContent = text;

                // Enable copy button
                copyBtn.disabled = false;
            }

            // Process the image/PDF
            processBtn.addEventListener('click', async () => {
                if (!imagePreview.src || imagePreview.src === '#') {
                    alert('Vui lòng tải lên hình ảnh hoặc PDF trước.');
                    return;
                }

                // Start timing
                const startTime = Date.now();
                const fileType = window.selectedFile ?
                    (window.selectedFile.type === 'application/pdf' ? 'pdf' : 'image') : 'image';
                const fileSize = window.selectedFile ? window.selectedFile.size : 0;

                // Get estimated time and start progress tracking
                const estimation = timingManager.getEstimatedTime(fileType, fileSize);

                // Show loading indicator
                loadingIndicator.style.display = 'block';
                markdownOutput.innerHTML = '';
                sourceCode.textContent = '';
                copyBtn.disabled = true;
                processBtn.disabled = true;

                // Start progress tracking
                startProgressTracking(estimation.estimated);

                // Switch to rendered view
                viewRenderedBtn.click();

                // Streaming is disabled

                try {
                    const baseUrl = apiUrlInput.value.trim();

                    // Check if we have a PDF file to upload
                    if (window.selectedFile && window.selectedFile.type === 'application/pdf') {
                        // Use upload endpoint for PDF
                        const apiUrl = `${baseUrl}/api/upload-image`;
                        const formData = new FormData();
                        formData.append('file', window.selectedFile);
                        formData.append('prompt', getPromptValue().trim());
                        formData.append('max_tokens', maxTokensInput.value);
                        formData.append('temperature', temperatureInput.value);
                        formData.append('num_beams', beamsInput.value);
                        formData.append('stream', 'false'); // PDF doesn't support streaming yet
                        if (templateSelect.value) {
                            formData.append('template', templateSelect.value);
                        }

                        const response = await fetch(apiUrl, {
                            method: 'POST',
                            body: formData
                        });

                        if (!response.ok) {
                            throw new Error(`Server error: ${response.status}`);
                        }

                        const data = await response.json();

                        // Nếu có nhiều trang, chỉ lấy trang 1
                        if (data.text && data.text.includes('## Trang 1')) {
                            // Tách phần trang 1
                            const match = data.text.match(/## Trang 1[\s\S]*?(?=(## Trang 2|$))/);
                            if (match) {
                                renderMarkdown(match[0]);
                            } else {
                                renderMarkdown(data.text); // fallback nếu không tách được
                            }
                        } else {
                            renderMarkdown(data.text);
                        }

                        // Show page count if available
                        if (data.pages) {
                            markdownOutput.innerHTML = `<div class="alert alert-info">Đã xử lý ${data.pages} trang PDF</div>` + markdownOutput.innerHTML;
                        }

                        // Save timing for PDF processing
                        const endTime = Date.now();
                        timingManager.saveRequestTiming(startTime, endTime, fileType, fileSize);

                        // Stop progress tracking and hide loading indicator
                        stopProgressTracking();
                        loadingIndicator.style.display = 'none';
                    } else {
                        // Use image-to-text endpoint for images
                        const apiUrl = `${baseUrl}/api/image-to-text`;
                        const requestBody = {
                            prompt: getPromptValue().trim(),
                            image: imagePreview.src,
                            max_tokens: parseInt(maxTokensInput.value),
                            temperature: parseFloat(temperatureInput.value),
                            num_beams: parseInt(beamsInput.value),
                            stream: false,
                            template: templateSelect.value || null
                        };

                        // Handle regular (non-streaming) response
                        const response = await fetch(apiUrl, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(requestBody)
                        });

                        if (!response.ok) {
                            throw new Error(`Server error: ${response.status}`);
                        }

                        const data = await response.json();

                        // Render the markdown result
                        renderMarkdown(data.text);

                        // Save timing for regular response
                        const endTime = Date.now();
                        timingManager.saveRequestTiming(startTime, endTime, fileType, fileSize);

                        // Stop progress tracking and hide loading indicator
                        stopProgressTracking();
                        loadingIndicator.style.display = 'none';
                    }
                } catch (error) {
                    console.error('Error processing image:', error);
                    markdownOutput.innerHTML = `<div class="alert alert-danger">Lỗi: ${error.message}</div>`;
                    sourceCode.textContent = `Lỗi: ${error.message}`;

                    // Stop progress tracking on error
                    stopProgressTracking();
                    loadingIndicator.style.display = 'none';
                } finally {
                    processBtn.disabled = false;
                }
            });

            // Copy to clipboard
            copyBtn.addEventListener('click', () => {
                // Copy the original markdown text, not the rendered HTML
                const textToCopy = originalMarkdownText;

                navigator.clipboard.writeText(textToCopy)
                    .then(() => {
                        const originalText = copyBtn.innerHTML;
                        copyBtn.innerHTML = '<i class="bi bi-check"></i> Đã Sao Chép';
                        setTimeout(() => {
                            copyBtn.innerHTML = originalText;
                        }, 2000);
                    })
                    .catch(err => {
                        console.error('Could not copy text: ', err);
                        alert('Không thể sao chép vào clipboard. Vui lòng thử lại.');
                    });
            });

            // Initialize Monaco Editor after DOM is ready
            require.config({ paths: { vs: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.44.0/min/vs' } });
            require(['vs/editor/editor.main'], function () {
                promptEditor = monaco.editor.create(document.getElementById('promptEditor'), {
                    value: 'Trích xuất thông tin chính trong ảnh và trả về dạng markdown.',
                    language: 'markdown',
                    theme: 'vs',
                    automaticLayout: true,
                    wordWrap: 'on',
                    minimap: { enabled: false },
                    scrollBeyondLastLine: false,
                    fontSize: 14,
                    lineNumbers: 'on',
                    folding: true,
                    renderWhitespace: 'selection',
                    contextmenu: true,
                    selectOnLineNumbers: true,
                    roundedSelection: false,
                    readOnly: false,
                    cursorStyle: 'line',
                    automaticLayout: true,
                    glyphMargin: false,
                    lineDecorationsWidth: 0,
                    lineNumbersMinChars: 3,
                    overviewRulerLanes: 0,
                    hideCursorInOverviewRuler: true,
                    scrollbar: {
                        vertical: 'auto',
                        horizontal: 'auto',
                        verticalScrollbarSize: 10,
                        horizontalScrollbarSize: 10
                    }
                });

                // Initialize template prompt and helper after editor is ready
                updateTemplatePromptAndHelper();
            });
        }); // End of DOMContentLoaded
    </script>
</body>

</html>